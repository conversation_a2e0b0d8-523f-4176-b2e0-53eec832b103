/**
 * Overlord Mastery System
 * Handles the Overlord Mastery progression system that unlocks at level 200
 * Allows users to invest overlord points into skills to improve character stats
 */

// Overlord mastery data is loaded via PHP wp_enqueue_script (like pet system)

// Define the system globally to ensure it's always available
window.OverlordMasterySystem = {
    // Track system state
    isInitialized: false,

    // Flag to indicate data has been loaded from storage
    dataLoaded: false,

    // Cache DOM elements
    elements: {},

    // Track active category
    activeCategory: 'attack',

    // Track skill levels for each category
    skillLevels: {
        attack: {},
        defense: {}
    },

    // Track used points
    usedPoints: 0,

    /**
     * Initialize the system
     */
    init: function() {
        console.log('OverlordMasterySystem: Starting initialization...');

        // Prevent multiple initializations
        if (this.isInitialized) {
            console.log('OverlordMasterySystem: Already initialized, skipping');
            return;
        }

        // Get the system panel
        const panel = document.getElementById('fg-overlord-mastery-system');
        if (!panel) {
            console.log('OverlordMasterySystem: Panel not found with ID fg-overlord-mastery-system');
            return;
        }
        console.log('OverlordMasterySystem: Panel found');

        // Check if data is available
        if (typeof OverlordMasteryData === 'undefined') {
            console.log('OverlordMasterySystem: OverlordMasteryData not loaded, retrying...');
            // Try again later if data not loaded yet
            setTimeout(() => this.init(), 100);
            return;
        }
        console.log('OverlordMasterySystem: OverlordMasteryData loaded');

        // Check if StatsConfig is available
        if (typeof StatsConfig === 'undefined') {
            console.log('OverlordMasterySystem: StatsConfig not loaded, retrying...');
            // Try again later if StatsConfig not loaded yet
            setTimeout(() => this.init(), 100);
            return;
        }
        console.log('OverlordMasterySystem: StatsConfig loaded');

        // Cache DOM elements
        this.elements.panel = panel;



        // Initialize UI
        this.initUI();

        // Initialize skill levels to 0
        this.initializeSkillLevels();

        // Load data from the central store if available
        this.loadFromStore();

        // Update stats to reflect initial state
        this.updateOverlordMasteryStats();

        // Mark as initialized
        this.isInitialized = true;
    },

    /**
     * Initialize the user interface
     */
    initUI: function() {
        // Replace the overlord mastery system placeholder with actual content
        this.createOverlordMasteryUI();
    },

    // Create the overlord mastery system UI
    createOverlordMasteryUI: function() {
        if (!this.elements.panel) {
            return;
        }

        // Create tab content for each category
        let categoryContentHTML = '';
        Object.keys(OverlordMasteryData.categories).forEach(categoryId => {
            const category = OverlordMasteryData.categories[categoryId];
            const isActive = categoryId === this.activeCategory;

            categoryContentHTML += `
                <div class="fg-overlord-mastery-panel ${isActive ? 'active' : ''}" data-category="${categoryId}">
                    <div class="fg-overlord-mastery-skills-grid" id="fg-overlord-mastery-${categoryId}-skills">
                        ${this.createSkillsHTML(categoryId)}
                    </div>
                </div>
            `;
        });

        // Create UI for overlord mastery system
        const overlordMasteryHTML = `
            <div class="fg-overlord-mastery-container">
                <div class="fg-overlord-mastery-header">
                    <h2>Overlord Mastery (Level 200+)</h2>
                    <div class="fg-overlord-mastery-points">
                        Points Used: <span class="used-points">0</span>
                    </div>
                </div>

                <!-- Tab Navigation -->
                <div class="fg-overlord-mastery-tabs">
                    ${Object.keys(OverlordMasteryData.categories).map(categoryId => {
                        const category = OverlordMasteryData.categories[categoryId];
                        const isActive = categoryId === this.activeCategory;
                        return `<div class="fg-overlord-mastery-tab ${isActive ? 'active' : ''}" data-category="${categoryId}">${category.name}</div>`;
                    }).join('')}
                </div>

                <!-- Tab Content -->
                <div class="fg-overlord-mastery-content">
                    ${categoryContentHTML}
                </div>

                <!-- Stats Summary Section -->
                <div class="fg-overlord-mastery-summary">
                    <h3>Mastery Stats Summary</h3>
                    <div class="fg-overlord-mastery-summary-content">
                        <!-- Stats will be dynamically populated here -->
                    </div>
                </div>
            </div>
        `;

        // Replace placeholder with our UI
        this.elements.panel.innerHTML = overlordMasteryHTML;

        // Cache additional elements
        this.elements.container = this.elements.panel.querySelector('.fg-overlord-mastery-container');
        this.elements.pointsDisplay = this.elements.panel.querySelector('.fg-overlord-mastery-points');
        this.elements.tabNav = this.elements.panel.querySelector('.fg-overlord-mastery-tabs');
        this.elements.tabContent = this.elements.panel.querySelector('.fg-overlord-mastery-content');
        this.elements.summaryContent = this.elements.panel.querySelector('.fg-overlord-mastery-summary-content');

        // Set up tab click handlers
        this.setupTabClickHandlers();

        // Set up skill click handlers
        this.setupSkillClickHandlers();

        // Update points display
        this.updatePointsDisplay();
    },

    // Create HTML for skills in a category with grid positioning
    createSkillsHTML: function(categoryId) {
        const skills = OverlordMasteryData.skills[categoryId];
        if (!skills) return '';

        // Create a 4x4 grid array
        const grid = Array(4).fill(null).map(() => Array(4).fill(null));

        // Place skills in their grid positions
        skills.forEach(skill => {
            if (skill.gridPosition) {
                const row = skill.gridPosition.row - 1; // Convert to 0-based
                const col = skill.gridPosition.col - 1; // Convert to 0-based
                if (row >= 0 && row < 4 && col >= 0 && col < 4) {
                    grid[row][col] = skill;
                }
            }
        });

        // Generate HTML for the grid
        let html = '';
        for (let row = 0; row < 4; row++) {
            for (let col = 0; col < 4; col++) {
                const skill = grid[row][col];
                if (skill) {
                    html += this.createSkillHTML(skill, categoryId);
                } else {
                    // Empty grid cell
                    html += `<div class="fg-overlord-mastery-skill-empty" style="grid-row: ${row + 1}; grid-column: ${col + 1};"></div>`;
                }
            }
        }

        return html;
    },

    // Create HTML for a single skill
    createSkillHTML: function(skill, categoryId) {
        const currentLevel = this.skillLevels[categoryId][skill.id] || 0;
        const currentValue = this.calculateSkillValue(skill, currentLevel);
        const hasSelection = currentLevel > 0;
        const isMaxed = currentLevel >= skill.maxLevel;

        // Get stat icon URL
        let iconUrl = '';
        if (typeof StatsConfig !== 'undefined') {
            iconUrl = StatsConfig.getStatIconUrl(skill.statId);
        }

        // Grid positioning
        const gridStyle = skill.gridPosition ?
            `grid-row: ${skill.gridPosition.row}; grid-column: ${skill.gridPosition.col};` : '';

        // Format value with + prefix if greater than 0
        const displayValue = currentValue > 0 ?
            `+${skill.isPercentage ? currentValue + '%' : currentValue}` :
            (skill.isPercentage ? '0%' : '0');

        return `
            <div class="fg-overlord-mastery-skill ${hasSelection ? 'selected' : 'empty'} ${isMaxed ? 'maxed' : ''}"
                 data-skill-id="${skill.id}"
                 data-category-id="${categoryId}"
                 style="${gridStyle}"
                 title="${skill.name}: 0-${skill.values[skill.maxLevel - 1] || 0}${skill.isPercentage ? '%' : ''} (Level ${currentLevel}/${skill.maxLevel})">
                <div class="fg-overlord-mastery-skill-slot ${hasSelection ? 'selected' : 'empty'}">
                    <div class="fg-overlord-mastery-skill-icon">
                        <img src="${iconUrl}" alt="${skill.name}">
                        <div class="fg-overlord-mastery-skill-level">${currentLevel}/${skill.maxLevel}</div>
                    </div>
                </div>
                <div class="fg-overlord-mastery-skill-value">${displayValue}</div>
                <div class="fg-overlord-mastery-skill-name">${skill.name}</div>
            </div>
        `;
    },

    // Setup tab click handlers
    setupTabClickHandlers: function() {
        if (!this.elements.tabNav) return;

        this.elements.tabNav.addEventListener('click', (e) => {
            const tab = e.target.closest('.fg-overlord-mastery-tab');
            if (!tab) return;

            const categoryId = tab.dataset.category;
            this.switchCategory(categoryId);
        });
    },

    // Setup skill click handlers
    setupSkillClickHandlers: function() {
        if (!this.elements.container) return;

        this.elements.container.addEventListener('click', (e) => {
            const skill = e.target.closest('.fg-overlord-mastery-skill');
            if (!skill) return;

            const skillId = skill.dataset.skillId;
            const categoryId = skill.dataset.categoryId;

            if (e.ctrlKey || e.metaKey) {
                // Decrease level with Ctrl+Click
                this.decreaseSkillLevel(skillId, categoryId);
            } else {
                // Increase level with normal click
                this.increaseSkillLevel(skillId, categoryId);
            }
        });
    },



    /**
     * Initialize skill levels to 0
     */
    initializeSkillLevels: function() {
        Object.keys(OverlordMasteryData.categories).forEach(categoryId => {
            this.skillLevels[categoryId] = {};

            OverlordMasteryData.skills[categoryId].forEach(skill => {
                this.skillLevels[categoryId][skill.id] = 0;
            });
        });

        // Reset used points
        this.usedPoints = 0;
    },



    /**
     * Increase skill level
     * @param {string} skillId - The skill ID
     * @param {string} categoryId - The category ID
     */
    increaseSkillLevel: function(skillId, categoryId) {
        // Find the skill
        const skill = this.findSkill(skillId, categoryId);
        if (!skill) return;

        // Get current level
        const currentLevel = this.skillLevels[categoryId][skillId] || 0;

        // Check if at max level
        if (currentLevel >= skill.maxLevel) return;

        // Get points needed
        const pointsNeeded = skill.opRequired || 1;

        // Increase level
        this.skillLevels[categoryId][skillId] = currentLevel + 1;

        // Update used points
        this.usedPoints += pointsNeeded;

        // Update UI
        this.updateSkillUI(skill, categoryId);
        this.updatePointsDisplay();

        // Update stats
        this.updateOverlordMasteryStats();

        // Save to store
        this.saveToStore();
    },

    /**
     * Decrease skill level
     * @param {string} skillId - The skill ID
     * @param {string} categoryId - The category ID
     */
    decreaseSkillLevel: function(skillId, categoryId) {
        // Find the skill
        const skill = this.findSkill(skillId, categoryId);
        if (!skill) return;

        // Get current level
        const currentLevel = this.skillLevels[categoryId][skillId] || 0;

        // Check if at minimum level
        if (currentLevel <= 0) return;

        // Get points that will be refunded
        const pointsRefunded = skill.opRequired || 1;

        // Decrease level
        this.skillLevels[categoryId][skillId] = currentLevel - 1;

        // Update used points
        this.usedPoints -= pointsRefunded;

        // Update UI
        this.updateSkillUI(skill, categoryId);
        this.updatePointsDisplay();

        // Update stats
        this.updateOverlordMasteryStats();

        // Save to store
        this.saveToStore();
    },

    /**
     * Find skill by ID and category
     * @param {string} skillId - The skill ID
     * @param {string} categoryId - The category ID
     * @returns {Object|null} The skill data or null if not found
     */
    findSkill: function(skillId, categoryId) {
        if (!OverlordMasteryData.skills[categoryId]) return null;

        return OverlordMasteryData.skills[categoryId].find(skill => skill.id === skillId);
    },

    /**
     * Update skill UI
     * @param {Object} skill - The skill data
     * @param {string} categoryId - The category ID
     */
    updateSkillUI: function(skill, categoryId) {
        // Find skill element
        const skillElement = this.elements.container.querySelector(`.fg-overlord-mastery-skill[data-skill-id="${skill.id}"][data-category-id="${categoryId}"]`);
        if (!skillElement) return;

        // Get current level
        const currentLevel = this.skillLevels[categoryId][skill.id] || 0;
        const currentValue = this.calculateSkillValue(skill, currentLevel);
        const hasSelection = currentLevel > 0;
        const isMaxed = currentLevel >= skill.maxLevel;

        // Update main skill element classes
        if (hasSelection) {
            skillElement.classList.add('selected');
            skillElement.classList.remove('empty');
        } else {
            skillElement.classList.remove('selected');
            skillElement.classList.add('empty');
        }

        if (isMaxed) {
            skillElement.classList.add('maxed');
        } else {
            skillElement.classList.remove('maxed');
        }

        // Update skill slot classes
        const skillSlot = skillElement.querySelector('.fg-overlord-mastery-skill-slot');
        if (skillSlot) {
            if (hasSelection) {
                skillSlot.classList.add('selected');
                skillSlot.classList.remove('empty');
            } else {
                skillSlot.classList.remove('selected');
                skillSlot.classList.add('empty');
            }
        }

        // Update level display
        const levelDisplay = skillElement.querySelector('.fg-overlord-mastery-skill-level');
        if (levelDisplay) {
            levelDisplay.textContent = `${currentLevel}/${skill.maxLevel}`;
        }

        // Update value display with + prefix
        const statValue = skillElement.querySelector('.fg-overlord-mastery-skill-value');
        if (statValue) {
            const displayValue = currentValue > 0 ?
                `+${skill.isPercentage ? currentValue + '%' : currentValue}` :
                (skill.isPercentage ? '0%' : '0');
            statValue.textContent = displayValue;
        }
    },

    /**
     * Update points display
     */
    updatePointsDisplay: function() {
        if (!this.elements.pointsDisplay) return;

        const usedPointsElement = this.elements.pointsDisplay.querySelector('.used-points');
        if (usedPointsElement) {
            usedPointsElement.textContent = this.usedPoints;
        }
    },

    /**
     * Calculate skill value at current level
     * @param {Object} skill - The skill data
     * @param {number} level - The skill level
     * @returns {number} The calculated value
     */
    calculateSkillValue: function(skill, level) {
        if (level <= 0) return 0;
        if (level > skill.maxLevel) level = skill.maxLevel;

        // Return the value from the values array (level is 1-based, array is 0-based)
        return skill.values[level - 1] || 0;
    },



    /**
     * Update overlord mastery stats
     */
    updateOverlordMasteryStats: function() {
        // Calculate stats from all skills
        const stats = this.calculateStats();

        // Update the stats in the build planner (using same method as all other systems)
        if (typeof BuildPlanner !== 'undefined' && BuildPlanner.updateStats) {
            BuildPlanner.updateStats('overlord-mastery', stats);
        }

        // Update the stats summary in our system UI
        this.updateStatsSummary(stats);
    },

    /**
     * Calculate stats from all skills
     * @returns {Object} The calculated stats
     */
    calculateStats: function() {
        const stats = {};

        // Process each category
        Object.keys(this.skillLevels).forEach(categoryId => {
            const categorySkills = OverlordMasteryData.skills[categoryId];

            // Process each skill in the category
            categorySkills.forEach(skill => {
                const level = this.skillLevels[categoryId][skill.id] || 0;
                if (level <= 0) return;

                // Calculate skill value
                const value = this.calculateSkillValue(skill, level);

                // Add to stats
                if (!stats[skill.statId]) {
                    stats[skill.statId] = 0;
                }
                stats[skill.statId] += value;
            });
        });

        return stats;
    },

    /**
     * Update stats summary in UI
     * @param {Object} stats - The calculated stats
     */
    updateStatsSummary: function(stats) {
        if (!this.elements.summaryContent) return;

        // Use StatIntegrationService for consistent stat display (like pet, honor, costume systems)
        if (typeof StatIntegrationService !== 'undefined') {
            this.elements.summaryContent.innerHTML = StatIntegrationService.createStatSummaryHTML(stats);
            return;
        }

        // Simple fallback if service isn't available
        this.elements.summaryContent.innerHTML = '<p class="no-stats">No overlord mastery stats selected yet.</p>';
    },

    /**
     * Get the essential data for saving
     * @returns {Object} The essential data
     */
    getEssentialData: function() {
        return {
            skillLevels: JSON.parse(JSON.stringify(this.skillLevels)),
            usedPoints: this.usedPoints
        };
    },

    /**
     * Save to store
     */
    saveToStore: function() {
        // Use the central BuildSaverStore to save data
        if (typeof BuildSaverStore !== 'undefined' && BuildSaverStore.saveData) {
            const essentialData = this.getEssentialData();
            BuildSaverStore.saveData('overlord-mastery', essentialData);
            return true;
        }

        return false;
    },

    /**
     * Load from store
     */
    loadFromStore: function() {
        // Initialize with default values first
        this.initializeSkillLevels();

        // Check if BuildSaverStore exists and has data
        if (window.BuildSaverStore && BuildSaverStore.dataLoaded) {
            // Get data for this system
            const systemData = BuildSaverStore.getSystemData('overlord-mastery');
            if (systemData) {
                return this.loadFromData(systemData);
            }
        }

        return false;
    },

    /**
     * Load from data
     * @param {Object} data - The data to load
     * @returns {boolean} True if loaded successfully
     */
    loadFromData: function(data) {
        if (!data) return false;

        // Load skill levels
        if (data.skillLevels) {
            Object.keys(data.skillLevels).forEach(categoryId => {
                if (!this.skillLevels[categoryId]) {
                    this.skillLevels[categoryId] = {};
                }

                Object.keys(data.skillLevels[categoryId]).forEach(skillId => {
                    this.skillLevels[categoryId][skillId] = data.skillLevels[categoryId][skillId];
                });
            });
        }

        // Load used points
        if (typeof data.usedPoints === 'number') {
            this.usedPoints = data.usedPoints;
        } else {
            // Recalculate used points if not provided
            this.recalculateUsedPoints();
        }

        // Update UI - refresh all skills and points display
        Object.keys(this.skillLevels).forEach(categoryId => {
            Object.keys(this.skillLevels[categoryId]).forEach(skillId => {
                const skill = this.findSkill(skillId, categoryId);
                if (skill) {
                    this.updateSkillUI(skill, categoryId);
                }
            });
        });
        this.updatePointsDisplay();

        // Update stats
        this.updateOverlordMasteryStats();

        return true;
    },

    /**
     * Recalculate used points
     */
    recalculateUsedPoints: function() {
        this.usedPoints = 0;

        Object.keys(this.skillLevels).forEach(categoryId => {
            Object.keys(this.skillLevels[categoryId]).forEach(skillId => {
                const level = this.skillLevels[categoryId][skillId];
                const skill = this.findSkill(skillId, categoryId);

                if (skill && level > 0) {
                    // Add up points for each level (opRequired per level)
                    this.usedPoints += (skill.opRequired || 1) * level;
                }
            });
        });
    },

    /**
     * Switch active category
     * @param {string} categoryId - The category ID to switch to
     */
    switchCategory: function(categoryId) {
        // Don't do anything if already on this category
        if (categoryId === this.activeCategory) return;

        // Update active category
        this.activeCategory = categoryId;

        // Update tab navigation
        const tabs = this.elements.tabNav.querySelectorAll('.fg-overlord-mastery-tab');
        tabs.forEach(tab => {
            if (tab.dataset.category === categoryId) {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });

        // Update tab content
        const panels = this.elements.tabContent.querySelectorAll('.fg-overlord-mastery-panel');
        panels.forEach(panel => {
            if (panel.dataset.category === categoryId) {
                panel.classList.add('active');
            } else {
                panel.classList.remove('active');
            }
        });
    }
};

// Create an alias for backward compatibility if needed
window.OverlordMasterySystem = window.OverlordMasterySystem;