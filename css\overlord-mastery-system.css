/**
 * Overlord Mastery System CSS
 * Updated to match pet/honor system styling
 */

:root {
  /* Base colors matching pet/honor systems */
  --fg-dark-bg: #1c1e22;
  --fg-border: #3c3f44;
  --fg-darker-bg: rgba(0, 0, 0, 0.2);
  --fg-text: #d0d0d0;

  /* Accent colors */
  --fg-accent: #66bb6a;
  --fg-highlight: #4c8e50;
  --fg-header: #ff3333;
  --fg-hover-bg: rgba(27, 94, 32, 0.2);

  /* Overlord specific colors */
  --fg-overlord-accent: #ff7e00;
  --fg-overlord-highlight: #ff9933;
}

/* Main container */
.fg-overlord-mastery-container {
    padding: 20px;
    max-width: 100%;
    background-color: #1c1e22;
    border-radius: 5px;
    border: 1px solid #3c3f44;
    box-sizing: border-box;
    color: #f0f0f0;
}

.fg-overlord-mastery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--fg-border);
}

.fg-overlord-mastery-header h2 {
    margin: 0;
    color: var(--fg-overlord-accent);
    text-shadow: 0 0 5px rgba(255, 126, 0, 0.5);
}

.fg-overlord-mastery-points {
    background: rgba(28, 30, 34, 0.5);
    padding: 5px 15px;
    border-radius: 5px;
    font-weight: bold;
    border: 1px solid var(--fg-border);
}

.fg-overlord-mastery-points .used-points {
    color: var(--fg-overlord-accent);
}

/* Tabs */
.fg-overlord-mastery-tabs {
    display: flex;
    margin-bottom: 0;
    border-bottom: none;
}

.fg-overlord-mastery-tab {
    padding: 10px 20px;
    background: rgba(28, 30, 34, 0.5);
    border-radius: 8px 8px 0 0;
    margin-right: 5px;
    cursor: pointer;
    border: 1px solid var(--fg-border);
    border-bottom: none;
    transition: all 0.3s;
    color: var(--fg-text);
}

.fg-overlord-mastery-tab:hover {
    background: rgba(60, 63, 68, 0.5);
}

.fg-overlord-mastery-tab.active {
    background: rgba(60, 63, 68, 0.5);
    color: var(--fg-overlord-accent);
    font-weight: bold;
    border-bottom: 1px solid rgba(60, 63, 68, 0.5);
    position: relative;
}

.fg-overlord-mastery-tab.active:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: rgba(60, 63, 68, 0.5);
}

/* Content Area */
.fg-overlord-mastery-content {
    background: rgba(60, 63, 68, 0.5);
    border: 1px solid var(--fg-border);
    border-radius: 0 8px 8px 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.fg-overlord-mastery-panel {
    display: none;
}

.fg-overlord-mastery-panel.active {
    display: block;
}

/* Skill Grid - 4x4 layout matching in-game */
.fg-overlord-mastery-skills-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(4, 1fr);
    gap: 12px;
    max-width: 500px;
    margin: 0 auto;
    padding: 10px;
    position: relative;
    z-index: 0; /* Keep grid in background */
    /* Simple solid vertical lines for each column */
    background-image:
        linear-gradient(rgba(255, 126, 0, 0.3), rgba(255, 126, 0, 0.3)),
        linear-gradient(rgba(255, 126, 0, 0.3), rgba(255, 126, 0, 0.3)),
        linear-gradient(rgba(255, 126, 0, 0.3), rgba(255, 126, 0, 0.3)),
        linear-gradient(rgba(255, 126, 0, 0.3), rgba(255, 126, 0, 0.3));
    background-size: 2px 100%, 2px 100%, 2px 100%, 2px 100%;
    background-position: 12.5% 0, 37.5% 0, 62.5% 0, 87.5% 0;
    background-repeat: no-repeat;
}

/* Skill Slot - Compact design like pet/honor systems */
.fg-overlord-mastery-skill {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
    z-index: 1; /* Keep skills above background lines */
}

.fg-overlord-mastery-skill:hover .fg-overlord-mastery-skill-slot {
    border-color: #4f5a68;
    box-shadow: inset 0 0 4px rgba(79, 90, 104, 0.7);
}

/* Skill states */
.fg-overlord-mastery-skill.selected .fg-overlord-mastery-skill-slot {
    border-color: var(--fg-highlight);
    background: rgba(27, 94, 32, 0.3);
}

.fg-overlord-mastery-skill.maxed .fg-overlord-mastery-skill-slot {
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.2);
}

/* Skill slot container */
.fg-overlord-mastery-skill-slot {
    position: relative;
    min-height: 50px;
    max-height: 55px;
    aspect-ratio: 1/1;
    border: 1px solid var(--fg-border);
    border-radius: 4px;
    background-color: var(--fg-darker-bg);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fg-overlord-mastery-skill-slot.empty {
    border-color: #333;
    background: linear-gradient(135deg, #1a1a1a 0%, #222 100%);
}

/* Empty grid cells */
.fg-overlord-mastery-skill-empty {
    min-height: 55px;
    background: transparent;
}

/* Skill icon */
.fg-overlord-mastery-skill-icon {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.fg-overlord-mastery-skill-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Level indicator overlay on icon */
.fg-overlord-mastery-skill-level {
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 0.7rem;
    color: var(--fg-overlord-accent);
    font-weight: bold;
    background: rgba(0, 0, 0, 0.7);
    padding: 1px 3px;
    border-radius: 2px;
    line-height: 1;
}

/* Stat value below slot with + prefix */
.fg-overlord-mastery-skill-value {
    font-size: 0.8em;
    color: #ffc107;
    font-weight: bold;
    text-align: center;
    line-height: 1;
    user-select: text;
    margin: 0;
}

/* Skill name below value */
.fg-overlord-mastery-skill-name {
    font-size: 0.75em;
    color: #e0e0e0; /* Brighter than var(--fg-text) for better visibility */
    text-align: center;
    line-height: 1.1;
    margin: 0;
    max-width: 60px;
    word-wrap: break-word;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8); /* Add subtle shadow for better readability */
}

/* Remove old skill styling - no longer needed */

/* Tooltips */
.skill-tooltip {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1a1a1a;
    border: 1px solid #444;
    border-radius: 8px;
    padding: 15px;
    width: 250px;
    z-index: 100;
    display: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.7);
}

.fg-overlord-mastery-skill:hover .skill-tooltip {
    display: block;
}

.skill-tooltip h4 {
    margin-top: 0;
    margin-bottom: 5px;
    color: #ff7e00;
    font-size: 16px;
}

.tooltip-level {
    color: #aaa;
    font-size: 12px;
    margin-bottom: 10px;
}

.tooltip-description {
    margin-bottom: 10px;
    font-size: 13px;
    line-height: 1.4;
}

.tooltip-effects {
    font-size: 12px;
}

.tooltip-effect {
    padding: 4px 8px;
    margin-bottom: 4px;
    border-radius: 4px;
    background: #2a2a2a;
}

.tooltip-effect.active {
    background: #3a3a3a;
    color: #ff7e00;
    font-weight: bold;
}

.tooltip-effect.inactive {
    color: #888;
}

/* Summary */
.fg-overlord-mastery-summary {
    background: rgba(28, 30, 34, 0.5);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid var(--fg-border);
}

.fg-overlord-mastery-summary h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--fg-overlord-accent);
    font-size: 1.1em;
}

.fg-overlord-mastery-summary-content {
    font-size: 14px;
    line-height: 1.5;
}

.fg-overlord-mastery-instructions ul {
    margin: 0;
    padding-left: 20px;
}

.fg-overlord-mastery-instructions li {
    margin-bottom: 5px;
    font-size: 14px;
}

/* Form */
.fg-overlord-mastery-save {
    background: #ff7e00;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
}

.fg-overlord-mastery-save:hover {
    background: #ff9933;
    box-shadow: 0 0 10px rgba(255, 126, 0, 0.5);
}

/* Responsive */
@media (max-width: 600px) {
    .fg-overlord-mastery-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .skill-icon {
        width: 48px;
        height: 48px;
    }

    .skill-tooltip {
        width: 200px;
    }
}